# Server Configuration
server.port=8080
spring.application.name=mcp-server

# Database Configuration (removed for simplified testing)
# No database dependencies - application runs without any database

# Kylas API Configuration
kylas.api.url=https://api.kylas.io/v1/leads
kylas.api.key=${KYLAS_API_KEY:your-api-key-here}

# Logging Configuration
logging.level.com.sell.mcp=INFO
logging.level.org.springframework.ai.mcp=DEBUG
logging.level.org.springframework.web=INFO

# Security Configuration (OAuth2 disabled for testing)
# spring.security.oauth2.resourceserver.jwt.issuer-uri=${OAUTH2_ISSUER_URI:}

# MCP Server Configuration
spring.ai.mcp.server.enabled=true

spring.ai.openai.api-key=sk